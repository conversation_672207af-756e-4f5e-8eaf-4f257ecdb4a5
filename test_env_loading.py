#!/usr/bin/env python3
"""
Test script to verify that the .env file loading is working correctly
in the FastAPI application.
"""

import os
import sys
from dotenv import load_dotenv

def test_env_loading():
    """Test that environment variables are loaded correctly from .env file."""
    
    print("=== Testing .env file loading ===")
    
    # Test 1: Check if .env file exists
    env_file = ".env"
    if not os.path.exists(env_file):
        print(f"❌ ERROR: {env_file} file not found!")
        return False
    else:
        print(f"✅ Found {env_file} file")
    
    # Test 2: Load environment variables
    print("\n--- Loading environment variables ---")
    load_dotenv()
    
    # Test 3: Check if GANDALF_API_KEY is loaded
    gandalf_api_key = os.getenv("GANDALF_API_KEY")
    if not gandalf_api_key:
        print("❌ ERROR: GANDALF_API_KEY not found in environment variables!")
        return False
    else:
        print(f"✅ GANDALF_API_KEY loaded successfully")
        print(f"   Length: {len(gandalf_api_key)} characters")
        print(f"   First 10 chars: {gandalf_api_key[:10]}...")
    
    # Test 4: Verify it's not empty
    if gandalf_api_key.strip() == "":
        print("❌ ERROR: GANDALF_API_KEY is empty!")
        return False
    else:
        print("✅ GANDALF_API_KEY is not empty")
    
    # Test 5: Check if it can be split (for multiple keys support)
    api_keys = [key.strip() for key in gandalf_api_key.split(",") if key.strip()]
    print(f"✅ Found {len(api_keys)} API key(s)")
    
    print("\n=== All tests passed! ===")
    return True

if __name__ == "__main__":
    success = test_env_loading()
    sys.exit(0 if success else 1)
